import React from 'react';
import { Card, Typography, Result, Button } from 'antd';
import { HomeOutlined } from '@ant-design/icons';

const { Title } = Typography;

const Warehouses = () => {
  return (
    <div>
      <Card>
        <Title level={3} style={{ marginBottom: 24 }}>
          Quản lý kho hàng
        </Title>
        
        <Result
          icon={<HomeOutlined style={{ color: '#1890ff' }} />}
          title="Module Kho hàng"
          subTitle="Tính năng quản lý kho hàng đang được phát triển. Sẽ bao gồm: danh sách kho, tồn kho theo phiên bả<PERSON> sả<PERSON> ph<PERSON>m, l<PERSON>ch sử xuất nhập kho, kiểm kê."
          extra={
            <Button type="primary" disabled>
              Đang phát triển
            </Button>
          }
        />
      </Card>
    </div>
  );
};

export default Warehouses;
