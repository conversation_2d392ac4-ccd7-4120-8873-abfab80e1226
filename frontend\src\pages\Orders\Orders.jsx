import React from 'react';
import { Card, Typography, Result, Button } from 'antd';
import { ShoppingCartOutlined } from '@ant-design/icons';

const { Title } = Typography;

const Orders = () => {
  return (
    <div>
      <Card>
        <Title level={3} style={{ marginBottom: 24 }}>
          Quản lý đơn hàng
        </Title>
        
        <Result
          icon={<ShoppingCartOutlined style={{ color: '#1890ff' }} />}
          title="Module Đơn hàng"
          subTitle="Tính năng quản lý đơn hàng đang được phát triển. Sẽ bao gồm: t<PERSON><PERSON> đơn hàng, x<PERSON> lý đơn hàng, thanh to<PERSON>, giao hàng, lịch sử đơn hàng."
          extra={
            <Button type="primary" disabled>
              Đang phát triển
            </Button>
          }
        />
      </Card>
    </div>
  );
};

export default Orders;
