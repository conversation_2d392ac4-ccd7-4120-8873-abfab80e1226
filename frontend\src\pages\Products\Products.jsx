import React from 'react';
import { Card, Typography, Result, Button } from 'antd';
import { ShoppingOutlined } from '@ant-design/icons';

const { Title } = Typography;

const Products = () => {
  return (
    <div>
      <Card>
        <Title level={3} style={{ marginBottom: 24 }}>
          Quản lý sản phẩm
        </Title>
        
        <Result
          icon={<ShoppingOutlined style={{ color: '#1890ff' }} />}
          title="Module Sản phẩm"
          subTitle="Tính năng quản lý sản phẩm đang được phát triển. Sẽ bao gồm: danh s<PERSON>ch sản phẩm, phi<PERSON><PERSON> b<PERSON><PERSON> sản ph<PERSON>, thu<PERSON><PERSON> t<PERSON>, hình <PERSON>, quản lý tồn kho."
          extra={
            <Button type="primary" disabled>
              Đang phát triển
            </Button>
          }
        />
      </Card>
    </div>
  );
};

export default Products;
