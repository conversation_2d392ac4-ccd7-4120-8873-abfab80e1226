import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  Layout as AntLayout,
  Menu,
  Avatar,
  Dropdown,
  Button,
  Typography,
  Space,
  Badge,
  Breadcrumb
} from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
  BellOutlined,
  DashboardOutlined,
  ShoppingOutlined,
  ShoppingCartOutlined,
  HomeOutlined,
  BarChartOutlined
} from '@ant-design/icons';

import { useAuth } from '../../contexts/AuthContext';
import { usePermissions } from '../../contexts/PermissionContext';

const { Header, Sider, Content } = AntLayout;
const { Title, Text } = Typography;

// Icon mapping for menu items
const iconMap = {
  DashboardOutlined: <DashboardOutlined />,
  ShoppingOutlined: <ShoppingOutlined />,
  ShoppingCartOutlined: <ShoppingCartOutlined />,
  UserOutlined: <UserOutlined />,
  HomeOutlined: <HomeOutlined />,
  BarChartOutlined: <BarChartOutlined />,
  SettingOutlined: <SettingOutlined />
};

const Layout = () => {
  const [collapsed, setCollapsed] = useState(false);
  const { user, logout } = useAuth();
  const { getAccessibleMenuItems, canAccessModule } = usePermissions();
  const navigate = useNavigate();
  const location = useLocation();

  // Get menu items based on user permissions
  const menuItems = getAccessibleMenuItems().map(item => ({
    key: item.key,
    icon: iconMap[item.icon] || <SettingOutlined />,
    label: item.label,
    onClick: item.path ? () => navigate(item.path) : undefined,
    children: item.children?.map(child => ({
      key: child.key,
      label: child.label,
      onClick: () => navigate(child.path)
    }))
  }));

  // Get current selected menu key
  const getCurrentMenuKey = () => {
    const path = location.pathname;
    if (path.startsWith('/dashboard')) return 'dashboard';
    if (path.startsWith('/products')) return 'products';
    if (path.startsWith('/orders')) return 'orders';
    if (path.startsWith('/customers')) return 'customers';
    if (path.startsWith('/warehouses')) return 'warehouses';
    if (path.startsWith('/reports')) return 'reports';
    if (path.startsWith('/users')) return 'users';
    if (path.startsWith('/roles')) return 'roles';
    if (path.startsWith('/permissions')) return 'permissions';
    return 'dashboard';
  };

  // Generate breadcrumb items
  const getBreadcrumbItems = () => {
    const path = location.pathname;
    const items = [{ title: 'Trang chủ' }];

    if (path.startsWith('/dashboard')) {
      items.push({ title: 'Tổng quan' });
    } else if (path.startsWith('/products')) {
      items.push({ title: 'Sản phẩm' });
    } else if (path.startsWith('/orders')) {
      items.push({ title: 'Đơn hàng' });
    } else if (path.startsWith('/customers')) {
      items.push({ title: 'Khách hàng' });
    } else if (path.startsWith('/warehouses')) {
      items.push({ title: 'Kho hàng' });
    } else if (path.startsWith('/reports')) {
      items.push({ title: 'Báo cáo' });
    } else if (path.startsWith('/users')) {
      items.push({ title: 'Hệ thống' }, { title: 'Người dùng' });
    } else if (path.startsWith('/roles')) {
      items.push({ title: 'Hệ thống' }, { title: 'Vai trò' });
    } else if (path.startsWith('/permissions')) {
      items.push({ title: 'Hệ thống' }, { title: 'Quyền' });
    }

    return items;
  };

  // User dropdown menu
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Thông tin cá nhân',
      onClick: () => navigate('/profile')
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Cài đặt',
      onClick: () => navigate('/settings')
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Đăng xuất',
      onClick: logout
    }
  ];

  return (
    <AntLayout style={{ minHeight: '100vh' }}>
      {/* Sidebar */}
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        style={{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
        }}
      >
        {/* Logo */}
        <div style={{ 
          height: 64, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <Title 
            level={4} 
            style={{ 
              color: 'white', 
              margin: 0,
              fontSize: collapsed ? 16 : 20
            }}
          >
            {collapsed ? 'S' : 'SAPO'}
          </Title>
        </div>

        {/* Menu */}
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[getCurrentMenuKey()]}
          items={menuItems}
          style={{ borderRight: 0 }}
        />
      </Sider>

      {/* Main Layout */}
      <AntLayout style={{ marginLeft: collapsed ? 80 : 200, transition: 'margin-left 0.2s' }}>
        {/* Header */}
        <Header style={{ 
          padding: '0 24px', 
          background: '#fff', 
          display: 'flex', 
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: '1px solid #f0f0f0'
        }}>
          {/* Left side */}
          <Space>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ fontSize: 16, width: 40, height: 40 }}
            />
          </Space>

          {/* Right side */}
          <Space size="middle">
            {/* Notifications */}
            <Badge count={0} showZero={false}>
              <Button 
                type="text" 
                icon={<BellOutlined />} 
                style={{ fontSize: 16 }}
              />
            </Badge>

            {/* User dropdown */}
            <Dropdown 
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              trigger={['click']}
            >
              <Space style={{ cursor: 'pointer' }}>
                <Avatar 
                  size="small" 
                  icon={<UserOutlined />}
                  style={{ backgroundColor: '#1890ff' }}
                />
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                  <Text strong style={{ fontSize: 14 }}>
                    {user?.ho_ten}
                  </Text>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    {user?.loai_nguoi_dung === 'admin' ? 'Quản trị viên' : 
                     user?.loai_nguoi_dung === 'nhan_vien' ? 'Nhân viên' : 'Khách hàng'}
                  </Text>
                </div>
              </Space>
            </Dropdown>
          </Space>
        </Header>

        {/* Content */}
        <Content style={{ 
          margin: '24px',
          minHeight: 'calc(100vh - 112px)',
          background: '#f0f2f5'
        }}>
          {/* Breadcrumb */}
          <Breadcrumb 
            items={getBreadcrumbItems()}
            style={{ marginBottom: 16 }}
          />

          {/* Page Content */}
          <div style={{ 
            background: '#fff',
            padding: 24,
            borderRadius: 8,
            minHeight: 'calc(100vh - 200px)'
          }}>
            <Outlet />
          </div>
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;
