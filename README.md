# Phần mềm quản lý bán hàng - SAPO Clone

Dự án clone phần mềm quản lý bán hàng kiểu Sapo với đầy đủ tính năng quản lý sản phẩm, đ<PERSON><PERSON> hàng, <PERSON><PERSON><PERSON><PERSON> hà<PERSON>, kho hàng và hệ thống phân quyền động.

## 🚀 Tính năng chính

### Backend (NodeJS + Express + MySQL)
- ✅ **Hệ thống xác thực**: JWT Authentication
- ✅ **Phân quyền động (RBAC)**: Quản lý vai trò và quyền linh hoạt
- ✅ **API RESTful**: Đầy đủ CRUD operations
- ✅ **Database**: MySQL với Sequelize ORM
- ✅ **Validation**: Joi validation cho input
- ✅ **Security**: Helmet, CORS, Rate limiting
- 🔄 **Quản lý sản phẩm**: Đang phát triển
- 🔄 **Quản lý đơn hàng**: <PERSON><PERSON> phát triển
- 🔄 **Quản lý kho hàng**: <PERSON><PERSON> phát triển

### Frontend (ReactJS + Vite)
- ✅ **UI Framework**: Ant Design
- ✅ **Routing**: React Router v6
- ✅ **State Management**: React Query + Context API
- ✅ **Authentication**: JWT với auto-refresh
- ✅ **Permission-based UI**: Hiện/ẩn theo quyền
- ✅ **Responsive Design**: Mobile-friendly
- ✅ **Dashboard**: Tổng quan với charts
- ✅ **User Management**: CRUD người dùng
- ✅ **Role Management**: CRUD vai trò
- ✅ **Permission Management**: CRUD quyền

## 🏗️ Kiến trúc hệ thống

```
├── backend/                 # Backend API (NodeJS + Express)
│   ├── config/             # Database config
│   ├── middleware/         # Auth, error handling
│   ├── models/            # Sequelize models
│   ├── routes/            # API routes
│   ├── seeders/           # Database seeders
│   └── server.js          # Entry point
├── frontend/              # Frontend (React + Vite)
│   ├── src/
│   │   ├── components/    # Reusable components
│   │   ├── contexts/      # React contexts
│   │   ├── pages/         # Page components
│   │   ├── services/      # API services
│   │   └── App.jsx        # Main app
└── package.json           # Root package.json
```

## 📋 Yêu cầu hệ thống

- **Node.js**: >= 16.0.0
- **MySQL**: >= 8.0
- **npm**: >= 8.0.0

## 🛠️ Cài đặt và chạy dự án

### 1. Clone repository
```bash
git clone <repository-url>
cd PhanMemBanHang
```

### 2. Cài đặt dependencies
```bash
npm run install:all
```

### 3. Cấu hình database
```bash
# Tạo database MySQL
mysql -u root -p
CREATE DATABASE phan_mem_ban_hang;
exit

# Copy và chỉnh sửa file .env
cp backend/.env.example backend/.env
```

Chỉnh sửa file `backend/.env`:
```env
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password
DB_DATABASE=phan_mem_ban_hang
JWT_SECRET=your_super_secret_jwt_key
```

### 4. Chạy migration và seed data
```bash
cd backend
npm install
npm run migrate
npm run seed
```

### 5. Khởi động ứng dụng

#### Development mode (cả backend và frontend)
```bash
npm run dev
```

#### Hoặc chạy riêng từng service

**Backend:**
```bash
npm run server:dev
# Server chạy tại: http://localhost:5000
```

**Frontend:**
```bash
npm run client:dev
# App chạy tại: http://localhost:3000
```

## 👥 Tài khoản demo

Sau khi seed data, bạn có thể sử dụng các tài khoản sau để test:

| Vai trò | Email | Mật khẩu | Mô tả |
|---------|-------|----------|-------|
| **Admin** | <EMAIL> | 123456 | Có tất cả quyền trong hệ thống |
| **Quản lý** | <EMAIL> | 123456 | Quản lý các hoạt động kinh doanh |
| **Nhân viên bán hàng** | <EMAIL> | 123456 | Thực hiện các giao dịch bán hàng |
| **Nhân viên kho** | <EMAIL> | 123456 | Quản lý kho hàng và tồn kho |
| **Kế toán** | <EMAIL> | 123456 | Quản lý tài chính và báo cáo |

## 🗄️ Database Schema

Hệ thống sử dụng các bảng chính:

### Core Tables
- `nguoi_dung` - Thông tin người dùng
- `vai_tro` - Định nghĩa vai trò
- `quyen` - Định nghĩa quyền
- `nguoi_dung_vai_tro` - Mapping user-role
- `vai_tro_quyen` - Mapping role-permission

### Business Tables (Đang phát triển)
- `san_pham` - Sản phẩm
- `phien_ban_san_pham` - Phiên bản sản phẩm
- `don_hang` - Đơn hàng
- `kho_hang` - Kho hàng
- `ton_kho_phien_ban` - Tồn kho

## 🔐 Hệ thống phân quyền

### Vai trò mặc định:
- **ADMIN**: Toàn quyền
- **QUAN_LY**: Quản lý kinh doanh
- **NHAN_VIEN_BAN_HANG**: Bán hàng
- **NHAN_VIEN_KHO**: Quản lý kho
- **KE_TOAN**: Báo cáo tài chính

### Quyền được phân theo module:
- **Người dùng**: XEM_NGUOI_DUNG, THEM_NGUOI_DUNG, SUA_NGUOI_DUNG, XOA_NGUOI_DUNG
- **Sản phẩm**: XEM_SAN_PHAM, THEM_SAN_PHAM, SUA_SAN_PHAM, XOA_SAN_PHAM
- **Đơn hàng**: XEM_DON_HANG, THEM_DON_HANG, SUA_DON_HANG, XOA_DON_HANG
- **Khách hàng**: XEM_KHACH_HANG, THEM_KHACH_HANG, SUA_KHACH_HANG, XOA_KHACH_HANG
- **Kho hàng**: XEM_KHO_HANG, THEM_KHO_HANG, SUA_KHO_HANG, XOA_KHO_HANG
- **Báo cáo**: XEM_BAO_CAO, XUAT_BAO_CAO

## 📚 API Documentation

### Authentication
- `POST /api/auth/login` - Đăng nhập
- `POST /api/auth/register` - Đăng ký
- `GET /api/auth/me` - Thông tin user hiện tại
- `POST /api/auth/change-password` - Đổi mật khẩu

### User Management
- `GET /api/users` - Danh sách người dùng
- `POST /api/users` - Tạo người dùng
- `PUT /api/users/:id` - Cập nhật người dùng
- `DELETE /api/users/:id` - Xóa người dùng

### Role Management
- `GET /api/roles` - Danh sách vai trò
- `POST /api/roles` - Tạo vai trò
- `PUT /api/roles/:id` - Cập nhật vai trò
- `DELETE /api/roles/:id` - Xóa vai trò

### Permission Management
- `GET /api/permissions` - Danh sách quyền
- `POST /api/permissions` - Tạo quyền
- `PUT /api/permissions/:id` - Cập nhật quyền
- `DELETE /api/permissions/:id` - Xóa quyền

## 🚧 Roadmap

### Phase 1 (Hoàn thành) ✅
- [x] Hệ thống authentication & authorization
- [x] Quản lý người dùng, vai trò, quyền
- [x] Dashboard cơ bản
- [x] UI/UX responsive

### Phase 2 (Đang phát triển) 🔄
- [ ] Module quản lý sản phẩm
- [ ] Module quản lý đơn hàng
- [ ] Module quản lý khách hàng
- [ ] Module quản lý kho hàng

### Phase 3 (Kế hoạch) 📋
- [ ] Module báo cáo chi tiết
- [ ] Tích hợp thanh toán
- [ ] Notification system
- [ ] Export/Import Excel
- [ ] Mobile app

## 🤝 Đóng góp

1. Fork dự án
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## 📝 License

Dự án này được phân phối dưới giấy phép MIT. Xem file `LICENSE` để biết thêm chi tiết.

## 📞 Liên hệ

- **Email**: <EMAIL>
- **GitHub**: [your-github-username](https://github.com/your-github-username)

---

⭐ Nếu dự án này hữu ích, hãy cho chúng tôi một star!
