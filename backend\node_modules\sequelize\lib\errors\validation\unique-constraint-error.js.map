{"version": 3, "sources": ["../../../src/errors/validation/unique-constraint-error.ts"], "sourcesContent": ["import { CommonErrorProperties, ErrorOptions } from '../base-error';\nimport ValidationError, { ValidationErrorItem } from '../validation-error';\n\ninterface UniqueConstraintErrorParent\n  extends Error,\n    Pick<CommonErrorProperties, 'sql'> {}\n\nexport interface UniqueConstraintErrorOptions extends ErrorOptions {\n  parent?: UniqueConstraintErrorParent;\n  original?: UniqueConstraintErrorParent;\n  errors?: ValidationErrorItem[];\n  fields?: Record<string, unknown>;\n  message?: string;\n}\n\n/**\n * Thrown when a unique constraint is violated in the database\n */\nclass UniqueConstraintError extends ValidationError implements CommonErrorProperties {\n  readonly parent: UniqueConstraintErrorParent;\n  readonly original: UniqueConstraintErrorParent;\n  readonly fields: Record<string, unknown>;\n  readonly sql: string;\n\n  constructor(options: UniqueConstraintErrorOptions) {\n    options = options ?? {};\n    options.parent = options.parent ?? { sql: '', name: '', message: '' };\n    options.message =\n      options.message || options.parent.message || 'Validation Error';\n    options.errors = options.errors ?? [];\n    super(options.message, options.errors, { stack: options.stack });\n\n    this.name = 'SequelizeUniqueConstraintError';\n    this.fields = options.fields ?? {};\n    this.parent = options.parent;\n    this.original = options.parent;\n    this.sql = options.parent.sql;\n  }\n}\n\nexport default UniqueConstraintError;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA,8BAAqD;AAiBrD,oCAAoC,gCAAiD;AAAA,EAMnF,YAAY,SAAuC;AAxBrD;AAyBI,cAAU,4BAAW;AACrB,YAAQ,SAAS,cAAQ,WAAR,YAAkB,EAAE,KAAK,IAAI,MAAM,IAAI,SAAS;AACjE,YAAQ,UACN,QAAQ,WAAW,QAAQ,OAAO,WAAW;AAC/C,YAAQ,SAAS,cAAQ,WAAR,YAAkB;AACnC,UAAM,QAAQ,SAAS,QAAQ,QAAQ,EAAE,OAAO,QAAQ;AAXjD;AACA;AACA;AACA;AAUP,SAAK,OAAO;AACZ,SAAK,SAAS,cAAQ,WAAR,YAAkB;AAChC,SAAK,SAAS,QAAQ;AACtB,SAAK,WAAW,QAAQ;AACxB,SAAK,MAAM,QAAQ,OAAO;AAAA;AAAA;AAI9B,IAAO,kCAAQ;", "names": []}