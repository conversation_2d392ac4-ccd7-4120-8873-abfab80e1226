'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class ThuocTinh extends Model {
    static associate(models) {
      // Quan hệ với giá trị thuộc tính (1-n)
      ThuocTinh.hasMany(models.GiaTriThuocTinh, {
        foreignKey: 'thuoc_tinh_id',
        as: 'giaTriList'
      });

      // Quan hệ với sản phẩm (n-n)
      ThuocTinh.belongsToMany(models.SanPham, {
        through: models.ThuocTinhSanPham,
        foreignKey: 'thuoc_tinh_id',
        otherKey: 'san_pham_id',
        as: 'sanPhamList'
      });
    }
  }

  ThuocTinh.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    ten: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 100]
      }
    },
    nguoi_tao: {
      type: DataTypes.STRING,
      allowNull: true
    },
    nguoi_cap_nhap: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'ThuocTinh',
    tableName: 'thuoc_tinh'
  });

  return ThuocTinh;
};
