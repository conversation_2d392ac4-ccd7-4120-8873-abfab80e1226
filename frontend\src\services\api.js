import axios from 'axios';
import { message } from 'antd';
import Cookies from 'js-cookie';

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          // Unauthorized - redirect to login
          Cookies.remove('token');
          if (window.location.pathname !== '/login') {
            message.error('<PERSON>ên đăng nhập đã hết hạn');
            window.location.href = '/login';
          }
          break;
        
        case 403:
          // Forbidden
          message.error(data.message || 'Bạn không có quyền thực hiện hành động này');
          break;
        
        case 404:
          // Not found
          message.error(data.message || 'Không tìm thấy tài nguyên');
          break;
        
        case 422:
          // Validation error
          if (data.errors && Array.isArray(data.errors)) {
            data.errors.forEach(err => {
              message.error(`${err.field}: ${err.message}`);
            });
          } else {
            message.error(data.message || 'Dữ liệu không hợp lệ');
          }
          break;
        
        case 500:
          // Server error
          message.error('Lỗi máy chủ, vui lòng thử lại sau');
          break;
        
        default:
          message.error(data.message || 'Có lỗi xảy ra');
      }
    } else if (error.request) {
      // Network error
      message.error('Không thể kết nối đến máy chủ');
    } else {
      // Other error
      message.error('Có lỗi xảy ra');
    }
    
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  getMe: () => api.get('/auth/me'),
  changePassword: (passwordData) => api.post('/auth/change-password', passwordData),
};

// Users API
export const usersAPI = {
  getUsers: (params) => api.get('/users', { params }),
  getUser: (id) => api.get(`/users/${id}`),
  createUser: (userData) => api.post('/users', userData),
  updateUser: (id, userData) => api.put(`/users/${id}`, userData),
  deleteUser: (id) => api.delete(`/users/${id}`),
  assignRoles: (id, roleIds) => api.post(`/users/${id}/assign-roles`, { vai_tro_ids: roleIds }),
};

// Roles API
export const rolesAPI = {
  getRoles: () => api.get('/roles'),
  getRole: (id) => api.get(`/roles/${id}`),
  createRole: (roleData) => api.post('/roles', roleData),
  updateRole: (id, roleData) => api.put(`/roles/${id}`, roleData),
  deleteRole: (id) => api.delete(`/roles/${id}`),
  assignPermissions: (id, permissionIds) => api.post(`/roles/${id}/assign-permissions`, { quyen_ids: permissionIds }),
};

// Permissions API
export const permissionsAPI = {
  getPermissions: () => api.get('/permissions'),
  getPermission: (id) => api.get(`/permissions/${id}`),
  createPermission: (permissionData) => api.post('/permissions', permissionData),
  updatePermission: (id, permissionData) => api.put(`/permissions/${id}`, permissionData),
  deletePermission: (id) => api.delete(`/permissions/${id}`),
};

// Products API
export const productsAPI = {
  getProducts: (params) => api.get('/products', { params }),
  getProduct: (id) => api.get(`/products/${id}`),
  createProduct: (productData) => api.post('/products', productData),
  updateProduct: (id, productData) => api.put(`/products/${id}`, productData),
  deleteProduct: (id) => api.delete(`/products/${id}`),
};

// Orders API
export const ordersAPI = {
  getOrders: (params) => api.get('/orders', { params }),
  getOrder: (id) => api.get(`/orders/${id}`),
  createOrder: (orderData) => api.post('/orders', orderData),
  updateOrder: (id, orderData) => api.put(`/orders/${id}`, orderData),
  deleteOrder: (id) => api.delete(`/orders/${id}`),
};

// Customers API
export const customersAPI = {
  getCustomers: (params) => api.get('/customers', { params }),
  getCustomer: (id) => api.get(`/customers/${id}`),
  createCustomer: (customerData) => api.post('/customers', customerData),
  updateCustomer: (id, customerData) => api.put(`/customers/${id}`, customerData),
  deleteCustomer: (id) => api.delete(`/customers/${id}`),
};

// Warehouses API
export const warehousesAPI = {
  getWarehouses: (params) => api.get('/warehouses', { params }),
  getWarehouse: (id) => api.get(`/warehouses/${id}`),
  createWarehouse: (warehouseData) => api.post('/warehouses', warehouseData),
  updateWarehouse: (id, warehouseData) => api.put(`/warehouses/${id}`, warehouseData),
  deleteWarehouse: (id) => api.delete(`/warehouses/${id}`),
};

// Health check API
export const healthAPI = {
  check: () => api.get('/health'),
};

export default api;
