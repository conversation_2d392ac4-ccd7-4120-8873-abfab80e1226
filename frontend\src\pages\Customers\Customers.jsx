import React from 'react';
import { Card, Typography, Result, Button } from 'antd';
import { UserOutlined } from '@ant-design/icons';

const { Title } = Typography;

const Customers = () => {
  return (
    <div>
      <Card>
        <Title level={3} style={{ marginBottom: 24 }}>
          Quản lý khách hàng
        </Title>
        
        <Result
          icon={<UserOutlined style={{ color: '#1890ff' }} />}
          title="Module Khách hàng"
          subTitle="Tính năng quản lý khách hàng đang được phát triển. Sẽ bao gồm: thông tin khách hàng, nh<PERSON><PERSON> kh<PERSON><PERSON> hàng, lịch sử mua hàng, tích điểm, công nợ."
          extra={
            <Button type="primary" disabled>
              Đang phát triển
            </Button>
          }
        />
      </Card>
    </div>
  );
};

export default Customers;
